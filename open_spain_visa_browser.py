#!/usr/bin/env python3
"""
打开西班牙签证预约网站的浏览器工具
用于手动观察和分析预约流程
"""

import webbrowser
import time
import json
from datetime import datetime

def open_spain_visa_website():
    """打开西班牙签证预约网站"""
    
    # 西班牙签证预约网站的主要URL
    urls = {
        "主页": "https://spain.blscn.cn",
        "新预约": "https://spain.blscn.cn/CHN/appointment/newappointment",
        "登录页面": "https://spain.blscn.cn/CHN/account/login",
        "注册页面": "https://spain.blscn.cn/CHN/account/register"
    }
    
    print("=== 西班牙签证预约网站浏览器工具 ===")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n可访问的页面:")
    
    for name, url in urls.items():
        print(f"  {name}: {url}")
    
    print("\n正在打开浏览器...")
    
    # 依次打开主要页面
    for name, url in urls.items():
        print(f"打开 {name}...")
        webbrowser.open(url)
        time.sleep(2)  # 间隔2秒避免同时打开太多标签页
    
    print("\n浏览器已打开，请手动观察以下内容:")
    print("1. 页面是否正常加载")
    print("2. 是否有验证码或其他安全措施")
    print("3. 表单参数和隐藏字段")
    print("4. 网络请求（可通过F12开发者工具查看）")
    print("5. JavaScript代码中的关键参数")
    
    # 生成观察指南
    observation_guide = {
        "timestamp": datetime.now().isoformat(),
        "observation_checklist": {
            "页面加载": [
                "主页是否正常显示",
                "新预约页面是否可以访问",
                "是否有反爬虫机制（如Cloudflare）",
                "页面加载速度如何"
            ],
            "表单分析": [
                "查看页面源代码中的隐藏字段",
                "记录__RequestVerificationToken的值",
                "观察表单的action属性",
                "检查是否有动态生成的参数"
            ],
            "网络请求": [
                "打开F12开发者工具",
                "切换到Network标签页",
                "刷新页面观察请求",
                "记录关键API接口的URL和参数"
            ],
            "JavaScript分析": [
                "查看页面中的JavaScript代码",
                "寻找API调用相关的函数",
                "记录参数生成逻辑",
                "观察是否有加密或混淆"
            ],
            "安全机制": [
                "是否有验证码",
                "是否有频率限制",
                "是否需要登录才能访问",
                "是否有IP限制"
            ]
        },
        "key_apis_to_test": [
            "/CHN/appointment/GetAvailableAppointmentDates",
            "/CHN/appointment/GetAvailableSlotsByDate", 
            "/CHN/Appointment/slotSelection",
            "/CHN/Appointment/ApplicantSelection",
            "/CHN/payment/PaymentRequest"
        ],
        "parameters_to_extract": [
            "__RequestVerificationToken",
            "LocationId", 
            "AppointmentCategoryId",
            "VisaType",
            "day_key",
            "slot_key",
            "ResponseData"
        ]
    }
    
    # 保存观察指南
    with open('spain_visa_observation_guide.json', 'w', encoding='utf-8') as f:
        json.dump(observation_guide, f, ensure_ascii=False, indent=2)
    
    print(f"\n观察指南已保存到: spain_visa_observation_guide.json")
    print("\n建议观察步骤:")
    print("1. 首先查看主页和新预约页面的基本结构")
    print("2. 尝试填写表单，观察参数变化")
    print("3. 使用开发者工具监控网络请求")
    print("4. 分析关键API接口的调用方式")
    print("5. 记录发现的参数和接口信息")
    
    return observation_guide

def analyze_browser_findings():
    """分析浏览器观察结果"""
    print("\n=== 浏览器观察结果分析 ===")
    print("请根据浏览器中的观察，回答以下问题:")
    
    questions = [
        "1. 主页是否正常加载？(y/n)",
        "2. 新预约页面是否可以直接访问？(y/n)", 
        "3. 是否需要登录才能进行预约？(y/n)",
        "4. 页面中是否有验证码？(y/n)",
        "5. 是否观察到__RequestVerificationToken？(y/n)",
        "6. 网络请求中是否看到API调用？(y/n)",
        "7. 是否有明显的反爬虫机制？(y/n)"
    ]
    
    answers = {}
    for question in questions:
        while True:
            answer = input(question + " ").lower().strip()
            if answer in ['y', 'n', 'yes', 'no']:
                answers[question] = answer in ['y', 'yes']
                break
            else:
                print("请输入 y/n 或 yes/no")
    
    # 基于回答生成分析
    analysis = {
        "timestamp": datetime.now().isoformat(),
        "user_responses": answers,
        "analysis_results": {},
        "recommendations": []
    }
    
    # 分析结果
    if answers.get("1. 主页是否正常加载？(y/n)", False):
        analysis["analysis_results"]["accessibility"] = "网站可正常访问"
    else:
        analysis["analysis_results"]["accessibility"] = "网站访问受限"
        analysis["recommendations"].append("需要使用代理或VPN访问")
    
    if answers.get("3. 是否需要登录才能进行预约？(y/n)", False):
        analysis["analysis_results"]["authentication"] = "需要登录认证"
        analysis["recommendations"].append("必须先实现登录流程")
    else:
        analysis["analysis_results"]["authentication"] = "可匿名访问"
    
    if answers.get("4. 页面中是否有验证码？(y/n)", False):
        analysis["analysis_results"]["captcha"] = "存在验证码机制"
        analysis["recommendations"].append("需要验证码识别功能")
    else:
        analysis["analysis_results"]["captcha"] = "无验证码"
    
    if answers.get("6. 网络请求中是否看到API调用？(y/n)", False):
        analysis["analysis_results"]["api_visibility"] = "API调用可见"
        analysis["recommendations"].append("可以尝试直接调用API接口")
    else:
        analysis["analysis_results"]["api_visibility"] = "API调用不明显"
        analysis["recommendations"].append("需要深入分析页面交互")
    
    if answers.get("7. 是否有明显的反爬虫机制？(y/n)", False):
        analysis["analysis_results"]["anti_bot"] = "存在反爬虫机制"
        analysis["recommendations"].append("需要使用更复杂的绕过策略")
    else:
        analysis["analysis_results"]["anti_bot"] = "反爬虫机制不明显"
    
    # 综合建议
    if (answers.get("6. 网络请求中是否看到API调用？(y/n)", False) and 
        not answers.get("7. 是否有明显的反爬虫机制？(y/n)", False)):
        analysis["recommendations"].append("建议优先尝试直接API调用方式")
    else:
        analysis["recommendations"].append("建议继续使用页面解析方式")
    
    # 保存分析结果
    with open('browser_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析结果已保存到: browser_analysis_results.json")
    print("\n分析结果:")
    for key, value in analysis["analysis_results"].items():
        print(f"  {key}: {value}")
    
    print("\n建议:")
    for rec in analysis["recommendations"]:
        print(f"  - {rec}")
    
    return analysis

def main():
    """主函数"""
    print("西班牙签证预约流程分析工具")
    print("=" * 50)
    
    choice = input("请选择操作:\n1. 打开浏览器观察网站\n2. 分析观察结果\n3. 两者都执行\n请输入选择 (1/2/3): ").strip()
    
    if choice in ['1', '3']:
        print("\n正在打开浏览器...")
        observation_guide = open_spain_visa_website()
        
        if choice == '3':
            input("\n请在浏览器中完成观察后，按回车键继续...")
    
    if choice in ['2', '3']:
        print("\n开始分析观察结果...")
        analysis = analyze_browser_findings()
    
    print("\n分析完成！")
    print("相关文件:")
    print("- spain_visa_observation_guide.json: 观察指南")
    print("- browser_analysis_results.json: 分析结果")
    print("- spain_flow_analysis_report.json: 流程分析报告")

if __name__ == "__main__":
    main()
