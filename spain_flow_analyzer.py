#!/usr/bin/env python3
"""
西班牙签证预约流程分析工具
基于现有代码分析预约流程，判断是否可以直接调用接口
"""

import json
import re
from bs4 import BeautifulSoup
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SpainVisaFlowAnalyzer:
    def __init__(self):
        self.base_url = "https://spain.blscn.cn"
        
        # 从现有代码中提取的关键接口
        self.key_apis = {
            "new_appointment": "/CHN/appointment/newappointment",
            "available_dates": "/CHN/appointment/GetAvailableAppointmentDates", 
            "available_slots": "/CHN/appointment/GetAvailableSlotsByDate",
            "slot_selection": "/CHN/Appointment/slotSelection",
            "applicant_selection": "/CHN/Appointment/ApplicantSelection",
            "payment": "/CHN/Appointment/Payment",
            "payment_request": "/CHN/payment/PaymentRequest",
            "upload_photo": "/CHN/appointment/UploadApplicantPhoto",
            "verify_email": "/CHN/blsappointment/VerifyEmail"
        }
        
        # 从代码中提取的关键参数
        self.key_parameters = {
            "LocationId": "地区ID (如SHANGHAI)",
            "AppointmentCategoryId": "预约类别ID",
            "VisaType": "签证类型ID",
            "__RequestVerificationToken": "防伪令牌",
            "ResponseData": "响应数据JSON",
            "SelectedApplicants": "选中的申请人",
            "ApplicantPhotoId": "申请人照片ID",
            "EmailCode": "邮箱验证码",
            "TravelDate": "旅行日期",
            "ValueAddedServices": "增值服务",
            "PaymentGatewayType": "支付方式"
        }
        
    def analyze_current_flow(self):
        """分析当前预约流程"""
        logger.info("分析当前西班牙签证预约流程...")
        
        flow_analysis = {
            "flow_steps": [],
            "required_parameters": {},
            "api_dependencies": {},
            "page_parsing_requirements": {}
        }
        
        # 步骤1: 选择签证类型和地区
        step1 = {
            "step": 1,
            "description": "选择签证类型和地区",
            "url": self.key_apis["new_appointment"],
            "method": "GET -> POST",
            "required_params": ["LocationId", "AppointmentCategoryId", "VisaType"],
            "page_parsing": ["表单隐藏字段", "防伪令牌", "验证码处理"],
            "api_direct_possible": False,
            "reason": "需要从页面解析表单参数和防伪令牌"
        }
        flow_analysis["flow_steps"].append(step1)
        
        # 步骤2: 获取可用日期
        step2 = {
            "step": 2, 
            "description": "获取可用预约日期",
            "url": self.key_apis["available_dates"],
            "method": "POST",
            "required_params": ["locationId", "categoryId", "visaType"],
            "page_parsing": ["无需页面解析"],
            "api_direct_possible": True,
            "reason": "纯API调用，参数相对固定"
        }
        flow_analysis["flow_steps"].append(step2)
        
        # 步骤3: 获取可用时间段
        step3 = {
            "step": 3,
            "description": "获取指定日期的可用时间段", 
            "url": self.key_apis["available_slots"],
            "method": "POST",
            "required_params": ["date"],
            "page_parsing": ["无需页面解析"],
            "api_direct_possible": True,
            "reason": "纯API调用，只需要日期参数"
        }
        flow_analysis["flow_steps"].append(step3)
        
        # 步骤4: 选择时间段
        step4 = {
            "step": 4,
            "description": "确认选择的日期和时间段",
            "url": self.key_apis["slot_selection"],
            "method": "POST", 
            "required_params": ["day_key", "slot_key", "ResponseData", "SelectedApplicants", "__RequestVerificationToken"],
            "page_parsing": ["CSS样式解析获取day_key和slot_key", "JavaScript函数参数提取"],
            "api_direct_possible": False,
            "reason": "需要从页面CSS样式和JavaScript中解析动态参数"
        }
        flow_analysis["flow_steps"].append(step4)
        
        # 步骤5: 申请人信息确认
        step5 = {
            "step": 5,
            "description": "上传照片和确认申请人信息",
            "url": self.key_apis["applicant_selection"],
            "method": "POST",
            "required_params": ["ApplicantPhotoId", "EmailCode", "SelectedApplicants", "TravelDate"],
            "page_parsing": ["表单隐藏字段", "邮箱验证码"],
            "api_direct_possible": False,
            "reason": "需要邮箱验证码和照片上传ID"
        }
        flow_analysis["flow_steps"].append(step5)
        
        # 步骤6: 付款
        step6 = {
            "step": 6,
            "description": "选择付款方式并确认付款",
            "url": self.key_apis["payment_request"],
            "method": "POST",
            "required_params": ["ValueAddedServices", "PaymentGatewayType", "Id"],
            "page_parsing": ["付款页面表单解析", "服务选项解析"],
            "api_direct_possible": False,
            "reason": "需要从付款页面解析服务选项和表单参数"
        }
        flow_analysis["flow_steps"].append(step6)
        
        return flow_analysis
    
    def analyze_bypass_possibilities(self):
        """分析绕过页面解析的可能性"""
        logger.info("分析绕过页面解析的可能性...")
        
        bypass_analysis = {
            "fully_bypassable_steps": [],
            "partially_bypassable_steps": [],
            "non_bypassable_steps": [],
            "overall_assessment": "",
            "recommendations": []
        }
        
        flow = self.analyze_current_flow()
        
        for step in flow["flow_steps"]:
            if step["api_direct_possible"]:
                bypass_analysis["fully_bypassable_steps"].append({
                    "step": step["step"],
                    "description": step["description"],
                    "reason": step["reason"]
                })
            else:
                # 检查是否可以部分绕过
                if "防伪令牌" in step["page_parsing"] or "CSS样式解析" in step["page_parsing"]:
                    bypass_analysis["non_bypassable_steps"].append({
                        "step": step["step"],
                        "description": step["description"],
                        "blocking_factors": step["page_parsing"]
                    })
                else:
                    bypass_analysis["partially_bypassable_steps"].append({
                        "step": step["step"],
                        "description": step["description"],
                        "reason": "部分参数可以预设或缓存"
                    })
        
        # 整体评估
        total_steps = len(flow["flow_steps"])
        bypassable_steps = len(bypass_analysis["fully_bypassable_steps"])
        
        if bypassable_steps >= total_steps * 0.7:
            bypass_analysis["overall_assessment"] = "大部分流程可以绕过页面解析"
        elif bypassable_steps >= total_steps * 0.4:
            bypass_analysis["overall_assessment"] = "部分流程可以绕过页面解析"
        else:
            bypass_analysis["overall_assessment"] = "大部分流程需要页面解析"
        
        # 生成建议
        if bypassable_steps > 0:
            bypass_analysis["recommendations"].append(
                f"可以对步骤 {[s['step'] for s in bypass_analysis['fully_bypassable_steps']]} 实现直接API调用"
            )
        
        if len(bypass_analysis["non_bypassable_steps"]) > 0:
            bypass_analysis["recommendations"].append(
                "关键步骤仍需要页面解析，建议保持现有的页面解析机制"
            )
            
        bypass_analysis["recommendations"].append(
            "可以考虑混合模式：API直调 + 页面解析，提高效率的同时保证稳定性"
        )
        
        return bypass_analysis
    
    def analyze_current_implementation(self):
        """分析当前实现的优缺点"""
        logger.info("分析当前实现...")
        
        implementation_analysis = {
            "strengths": [
                "完整的错误处理和重试机制",
                "支持多种签证类型和地区",
                "自动处理验证码和邮箱验证",
                "完整的付款流程支持",
                "详细的日志记录和监控"
            ],
            "weaknesses": [
                "大量页面解析代码，维护成本高",
                "依赖页面结构，网站更新时容易失效",
                "CSS样式解析复杂，容易出错",
                "每个步骤都需要HTTP请求获取页面"
            ],
            "optimization_opportunities": [
                "缓存静态参数减少页面请求",
                "对稳定的API接口实现直接调用",
                "优化页面解析逻辑，提高解析效率",
                "实现参数模板化，减少动态解析"
            ]
        }
        
        return implementation_analysis
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        logger.info("生成综合分析报告...")
        
        report = {
            "analysis_timestamp": "2025-08-18T23:30:00",
            "flow_analysis": self.analyze_current_flow(),
            "bypass_analysis": self.analyze_bypass_possibilities(),
            "implementation_analysis": self.analyze_current_implementation(),
            "final_recommendations": []
        }
        
        # 最终建议
        bypass_ratio = len(report["bypass_analysis"]["fully_bypassable_steps"]) / len(report["flow_analysis"]["flow_steps"])
        
        if bypass_ratio >= 0.5:
            report["final_recommendations"].append(
                "建议实施混合模式：对可直调的API实现直接调用，对复杂步骤保持页面解析"
            )
        else:
            report["final_recommendations"].append(
                "建议保持现有页面解析模式，但可以优化解析逻辑和缓存机制"
            )
        
        report["final_recommendations"].extend([
            "优先优化步骤2和步骤3（日期和时间段查询），这两个API相对稳定",
            "保持步骤1、4、5、6的页面解析，因为涉及动态参数和安全验证",
            "实现参数缓存机制，减少重复的页面请求",
            "建立API监控，及时发现接口变化"
        ])
        
        return report

def main():
    """主函数"""
    analyzer = SpainVisaFlowAnalyzer()
    
    try:
        # 生成综合报告
        report = analyzer.generate_comprehensive_report()
        
        # 保存报告
        with open('spain_flow_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印关键结果
        print("\n=== 西班牙签证预约流程分析报告 ===")
        print(f"分析时间: {report['analysis_timestamp']}")
        
        print(f"\n流程总步骤数: {len(report['flow_analysis']['flow_steps'])}")
        print(f"可完全绕过页面解析的步骤: {len(report['bypass_analysis']['fully_bypassable_steps'])}")
        print(f"需要页面解析的步骤: {len(report['bypass_analysis']['non_bypassable_steps'])}")
        
        print(f"\n整体评估: {report['bypass_analysis']['overall_assessment']}")
        
        print("\n可直接调用API的步骤:")
        for step in report['bypass_analysis']['fully_bypassable_steps']:
            print(f"  - 步骤{step['step']}: {step['description']}")
        
        print("\n需要页面解析的步骤:")
        for step in report['bypass_analysis']['non_bypassable_steps']:
            print(f"  - 步骤{step['step']}: {step['description']}")
        
        print("\n最终建议:")
        for rec in report['final_recommendations']:
            print(f"  - {rec}")
        
        print(f"\n详细报告已保存到: spain_flow_analysis_report.json")
        
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")

if __name__ == "__main__":
    main()
