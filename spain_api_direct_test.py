#!/usr/bin/env python3
"""
西班牙签证预约接口直接调用测试
测试是否可以绕过页面解析直接调用API接口
"""

import json
import requests
import time
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SpainVisaAPITester:
    def __init__(self):
        self.base_url = "https://spain.blscn.cn"
        self.session = requests.Session()
        
        # 设置请求头模拟浏览器
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-C<PERSON>,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "X-Requested-With": "XMLHttpRequest",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Ch-Ua": '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"macOS"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        })
        
        # 关键API接口
        self.api_endpoints = {
            "available_dates": "/CHN/appointment/GetAvailableAppointmentDates",
            "available_slots": "/CHN/appointment/GetAvailableSlotsByDate",
            "slot_selection": "/CHN/Appointment/slotSelection",
            "applicant_selection": "/CHN/Appointment/ApplicantSelection", 
            "payment_request": "/CHN/payment/PaymentRequest",
            "upload_photo": "/CHN/appointment/UploadApplicantPhoto",
            "verify_email": "/CHN/blsappointment/VerifyEmail"
        }
        
        # 测试参数
        self.test_params = {
            "location_id": "SHANGHAI",
            "visa_type": "Tourism", 
            "category_id": "099a0161-b428-4a10-bb1e-639b7dee4fa0",  # 申根签证
            "test_date": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        }
        
    def test_get_available_dates(self):
        """测试获取可用日期接口"""
        logger.info("测试获取可用日期接口...")
        
        url = self.base_url + self.api_endpoints["available_dates"]
        
        # 尝试不同的参数组合
        test_cases = [
            # 无参数
            {},
            # 基本参数
            {
                "locationId": self.test_params["location_id"],
                "categoryId": self.test_params["category_id"]
            },
            # 完整参数
            {
                "locationId": self.test_params["location_id"], 
                "categoryId": self.test_params["category_id"],
                "visaType": self.test_params["visa_type"],
                "appointmentDate": self.test_params["test_date"]
            }
        ]
        
        results = []
        for i, params in enumerate(test_cases):
            try:
                logger.info(f"测试用例 {i+1}: {params}")
                
                # GET请求
                response_get = self.session.get(url, params=params, verify=False, timeout=15)
                
                # POST请求
                response_post = self.session.post(url, data=params, verify=False, timeout=15)
                
                result = {
                    "test_case": i+1,
                    "params": params,
                    "get_response": {
                        "status_code": response_get.status_code,
                        "content": response_get.text[:500],
                        "headers": dict(response_get.headers)
                    },
                    "post_response": {
                        "status_code": response_post.status_code, 
                        "content": response_post.text[:500],
                        "headers": dict(response_post.headers)
                    }
                }
                results.append(result)
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                results.append({
                    "test_case": i+1,
                    "params": params,
                    "error": str(e)
                })
                
        return results
    
    def test_get_available_slots(self):
        """测试获取可用时间段接口"""
        logger.info("测试获取可用时间段接口...")
        
        url = self.base_url + self.api_endpoints["available_slots"]
        
        # 测试不同日期
        test_dates = [
            self.test_params["test_date"],
            (datetime.now() + timedelta(days=60)).strftime("%Y-%m-%d"),
            (datetime.now() + timedelta(days=90)).strftime("%Y-%m-%d")
        ]
        
        results = []
        for date in test_dates:
            try:
                logger.info(f"测试日期: {date}")
                
                # 尝试不同的参数格式
                test_params = [
                    {"date": date},
                    {"appointmentDate": date},
                    {
                        "appointmentDate": date,
                        "locationId": self.test_params["location_id"],
                        "categoryId": self.test_params["category_id"]
                    }
                ]
                
                for params in test_params:
                    # GET请求 - 参数在URL中
                    response_get = self.session.get(f"{url}?date={date}", verify=False, timeout=15)
                    
                    # POST请求
                    response_post = self.session.post(url, data=params, verify=False, timeout=15)
                    
                    result = {
                        "date": date,
                        "params": params,
                        "get_response": {
                            "status_code": response_get.status_code,
                            "content": response_get.text[:500]
                        },
                        "post_response": {
                            "status_code": response_post.status_code,
                            "content": response_post.text[:500]
                        }
                    }
                    results.append(result)
                    
                    time.sleep(0.5)
                    
            except Exception as e:
                results.append({
                    "date": date,
                    "error": str(e)
                })
                
        return results
    
    def test_session_requirements(self):
        """测试会话和认证要求"""
        logger.info("测试会话和认证要求...")
        
        # 首先访问主页建立会话
        try:
            main_page = self.session.get(self.base_url + "/CHN/appointment/newappointment", verify=False, timeout=15)
            logger.info(f"主页访问状态: {main_page.status_code}")
            
            # 提取可能的token或session信息
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(main_page.text, 'html.parser')
            
            # 查找RequestVerificationToken
            token_input = soup.find('input', {'name': '__RequestVerificationToken'})
            if token_input:
                token = token_input.get('value')
                logger.info(f"找到RequestVerificationToken: {token[:20]}...")
                self.session.headers.update({"RequestVerificationToken": token})
            
            # 查找其他隐藏字段
            hidden_inputs = soup.find_all('input', {'type': 'hidden'})
            hidden_data = {}
            for inp in hidden_inputs:
                name = inp.get('name')
                value = inp.get('value')
                if name and value:
                    hidden_data[name] = value
            
            return {
                "main_page_status": main_page.status_code,
                "cookies": dict(self.session.cookies),
                "token": token if token_input else None,
                "hidden_fields": hidden_data
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def test_form_submission(self):
        """测试表单提交接口"""
        logger.info("测试表单提交接口...")
        
        # 模拟表单数据
        form_data = {
            "__RequestVerificationToken": "",  # 需要从页面获取
            "LocationId": self.test_params["location_id"],
            "AppointmentCategoryId": self.test_params["category_id"],
            "VisaType": self.test_params["visa_type"],
            "ResponseData": json.dumps({}),
            "SelectedApplicants": ""
        }
        
        url = self.base_url + self.api_endpoints["slot_selection"]
        
        try:
            response = self.session.post(url, data=form_data, verify=False, timeout=15)
            return {
                "status_code": response.status_code,
                "content": response.text[:1000],
                "url": response.url,
                "headers": dict(response.headers)
            }
        except Exception as e:
            return {"error": str(e)}
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("开始运行西班牙签证API综合测试...")
        
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "base_url": self.base_url,
            "test_params": self.test_params,
            "results": {}
        }
        
        # 1. 测试会话要求
        logger.info("1. 测试会话和认证要求")
        test_results["results"]["session_test"] = self.test_session_requirements()
        
        # 2. 测试获取可用日期
        logger.info("2. 测试获取可用日期接口")
        test_results["results"]["available_dates"] = self.test_get_available_dates()
        
        # 3. 测试获取可用时间段
        logger.info("3. 测试获取可用时间段接口")
        test_results["results"]["available_slots"] = self.test_get_available_slots()
        
        # 4. 测试表单提交
        logger.info("4. 测试表单提交接口")
        test_results["results"]["form_submission"] = self.test_form_submission()
        
        return test_results
    
    def analyze_results(self, test_results):
        """分析测试结果"""
        logger.info("分析测试结果...")
        
        analysis = {
            "can_bypass_page_parsing": False,
            "working_apis": [],
            "failed_apis": [],
            "recommendations": []
        }
        
        # 分析各个接口的测试结果
        for test_name, results in test_results["results"].items():
            if isinstance(results, list):
                # 处理列表结果
                success_count = 0
                for result in results:
                    if "error" not in result:
                        if (result.get("get_response", {}).get("status_code") == 200 or 
                            result.get("post_response", {}).get("status_code") == 200):
                            success_count += 1
                
                if success_count > 0:
                    analysis["working_apis"].append(test_name)
                else:
                    analysis["failed_apis"].append(test_name)
            else:
                # 处理单个结果
                if "error" not in results and results.get("status_code") == 200:
                    analysis["working_apis"].append(test_name)
                else:
                    analysis["failed_apis"].append(test_name)
        
        # 生成建议
        if len(analysis["working_apis"]) > len(analysis["failed_apis"]):
            analysis["can_bypass_page_parsing"] = True
            analysis["recommendations"].append("大部分API接口可以直接调用，建议尝试绕过页面解析")
        else:
            analysis["recommendations"].append("多数API接口需要特定认证，建议继续使用页面解析方式")
        
        if "session_test" in analysis["working_apis"]:
            analysis["recommendations"].append("需要先建立有效会话和获取认证token")
        
        return analysis

def main():
    """主函数"""
    tester = SpainVisaAPITester()
    
    try:
        # 运行综合测试
        test_results = tester.run_comprehensive_test()
        
        # 分析结果
        analysis = tester.analyze_results(test_results)
        
        # 合并结果
        final_report = {
            **test_results,
            "analysis": analysis
        }
        
        # 保存结果到文件
        with open('spain_api_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
        
        # 打印关键结果
        print("\n=== 西班牙签证API直接调用测试结果 ===")
        print(f"测试时间: {test_results['timestamp']}")
        print(f"\n是否可以绕过页面解析: {'是' if analysis['can_bypass_page_parsing'] else '否'}")
        print(f"\n可用的API接口: {', '.join(analysis['working_apis']) if analysis['working_apis'] else '无'}")
        print(f"失败的API接口: {', '.join(analysis['failed_apis']) if analysis['failed_apis'] else '无'}")
        
        print("\n建议:")
        for rec in analysis['recommendations']:
            print(f"- {rec}")
        
        print(f"\n详细结果已保存到: spain_api_test_results.json")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
