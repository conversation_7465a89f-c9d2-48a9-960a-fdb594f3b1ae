#!/usr/bin/env python3
"""
西班牙签证预约流程分析工具
用于分析预约流程是否可以直接调用接口而不解析页面参数
"""

import json
import requests
import time
from bs4 import BeautifulSoup
import logging
import re
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入selenium，如果没有安装则跳过浏览器功能
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logger.warning("Selenium未安装，将跳过浏览器功能")

class SpainVisaAnalyzer:
    def __init__(self):
        self.base_url = "https://spain.blscn.cn"
        self.session = requests.Session()
        self.driver = None
        
        # 设置请求头
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        })
        
        # 关键接口URL
        self.key_urls = {
            "new_appointment": "/CHN/appointment/newappointment",
            "slot_selection": "/CHN/Appointment/SlotSelection", 
            "available_dates": "/CHN/appointment/GetAvailableAppointmentDates",
            "available_slots": "/CHN/appointment/GetAvailableSlotsByDate",
            "applicant_selection": "/CHN/Appointment/ApplicantSelection",
            "payment": "/CHN/Appointment/Payment",
            "payment_request": "/CHN/payment/PaymentRequest",
            "verify_email": "/CHN/blsappointment/VerifyEmail"
        }
        
    def setup_browser(self):
        """设置浏览器"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        # chrome_options.add_argument("--headless")  # 注释掉以便观察
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.implicitly_wait(10)
        
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            
    def analyze_page_parameters(self, url, description=""):
        """分析页面参数"""
        logger.info(f"分析页面: {description} - {url}")
        
        try:
            # 使用requests获取页面
            response = self.session.get(url, verify=False, timeout=30)
            if response.status_code != 200:
                logger.error(f"页面请求失败: {response.status_code}")
                return None
                
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取表单参数
            forms = soup.find_all('form')
            form_data = {}
            
            for form in forms:
                form_info = {
                    'action': form.get('action', ''),
                    'method': form.get('method', 'GET'),
                    'inputs': []
                }
                
                inputs = form.find_all(['input', 'select', 'textarea'])
                for inp in inputs:
                    input_info = {
                        'name': inp.get('name', ''),
                        'type': inp.get('type', ''),
                        'value': inp.get('value', ''),
                        'id': inp.get('id', ''),
                        'required': inp.has_attr('required')
                    }
                    form_info['inputs'].append(input_info)
                    
                form_data[f"form_{len(form_data)}"] = form_info
            
            # 提取JavaScript中的关键变量
            scripts = soup.find_all('script')
            js_variables = {}
            
            for script in scripts:
                if script.string:
                    script_content = script.string
                    # 查找常见的变量模式
                    import re
                    
                    # 查找var声明
                    var_matches = re.findall(r'var\s+(\w+)\s*=\s*["\']([^"\']+)["\']', script_content)
                    for var_name, var_value in var_matches:
                        js_variables[var_name] = var_value
                        
                    # 查找JSON对象
                    json_matches = re.findall(r'(\w+)\s*=\s*(\{[^}]+\})', script_content)
                    for var_name, json_str in json_matches:
                        try:
                            js_variables[var_name] = json.loads(json_str)
                        except:
                            js_variables[var_name] = json_str
            
            return {
                'url': url,
                'status_code': response.status_code,
                'forms': form_data,
                'js_variables': js_variables,
                'cookies': dict(response.cookies),
                'headers': dict(response.headers)
            }
            
        except Exception as e:
            logger.error(f"分析页面参数失败: {e}")
            return None
    
    def analyze_network_requests(self, target_url):
        """使用浏览器分析网络请求"""
        if not self.driver:
            self.setup_browser()
            
        logger.info(f"使用浏览器分析网络请求: {target_url}")
        
        # 启用网络日志
        self.driver.execute_cdp_cmd('Network.enable', {})
        
        # 记录网络请求
        network_requests = []
        
        def log_request(request):
            network_requests.append({
                'url': request['request']['url'],
                'method': request['request']['method'],
                'headers': request['request'].get('headers', {}),
                'postData': request['request'].get('postData', '')
            })
        
        # 访问页面
        self.driver.get(target_url)
        time.sleep(3)
        
        # 获取网络日志
        logs = self.driver.get_log('performance')
        for log in logs:
            message = json.loads(log['message'])
            if message['message']['method'] == 'Network.requestWillBeSent':
                request_data = message['message']['params']
                network_requests.append({
                    'url': request_data['request']['url'],
                    'method': request_data['request']['method'],
                    'headers': request_data['request'].get('headers', {}),
                    'postData': request_data['request'].get('postData', '')
                })
        
        return network_requests
    
    def test_direct_api_calls(self):
        """测试直接API调用的可行性"""
        logger.info("测试直接API调用...")
        
        test_results = {}
        
        # 测试获取可用日期接口
        try:
            dates_url = self.base_url + self.key_urls["available_dates"]
            response = self.session.get(dates_url, verify=False, timeout=15)
            test_results["available_dates"] = {
                'status_code': response.status_code,
                'response': response.text[:500] if response.text else '',
                'success': response.status_code == 200
            }
        except Exception as e:
            test_results["available_dates"] = {'error': str(e), 'success': False}
        
        # 测试获取时间段接口
        try:
            slots_url = self.base_url + self.key_urls["available_slots"] + "?date=2024-12-01"
            response = self.session.get(slots_url, verify=False, timeout=15)
            test_results["available_slots"] = {
                'status_code': response.status_code,
                'response': response.text[:500] if response.text else '',
                'success': response.status_code == 200
            }
        except Exception as e:
            test_results["available_slots"] = {'error': str(e), 'success': False}
            
        return test_results
    
    def compare_parameters(self, page_params, api_params):
        """比较页面参数和API参数"""
        logger.info("比较页面参数和API参数...")
        
        comparison = {
            'page_only': [],
            'api_only': [],
            'common': [],
            'differences': []
        }
        
        # 这里需要根据实际获取的参数进行比较
        # 暂时返回空结果，实际使用时需要完善
        
        return comparison
    
    def generate_analysis_report(self):
        """生成分析报告"""
        logger.info("开始生成西班牙签证预约流程分析报告...")
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'analysis_results': {},
            'recommendations': []
        }
        
        # 分析主要页面
        main_pages = [
            (self.base_url + self.key_urls["new_appointment"], "新预约页面"),
            (self.base_url + self.key_urls["slot_selection"], "时间段选择页面"),
        ]
        
        for url, description in main_pages:
            page_analysis = self.analyze_page_parameters(url, description)
            if page_analysis:
                report['analysis_results'][description] = page_analysis
        
        # 测试直接API调用
        api_test_results = self.test_direct_api_calls()
        report['analysis_results']['api_tests'] = api_test_results
        
        # 生成建议
        if any(result.get('success', False) for result in api_test_results.values()):
            report['recommendations'].append("部分API接口可以直接调用，建议进一步测试参数要求")
        else:
            report['recommendations'].append("API接口需要特定的认证或参数，建议继续使用页面解析方式")
        
        return report

def main():
    """主函数"""
    analyzer = SpainVisaAnalyzer()
    
    try:
        # 生成分析报告
        report = analyzer.generate_analysis_report()
        
        # 保存报告到文件
        with open('spain_visa_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("分析报告已保存到 spain_visa_analysis_report.json")
        
        # 打印关键发现
        print("\n=== 西班牙签证预约流程分析结果 ===")
        print(f"分析时间: {report['timestamp']}")
        print("\n主要发现:")
        
        for description, analysis in report['analysis_results'].items():
            if 'status_code' in analysis:
                print(f"- {description}: HTTP {analysis['status_code']}")
            elif 'api_tests' in description:
                print(f"- API测试结果:")
                for api_name, result in analysis.items():
                    status = "成功" if result.get('success', False) else "失败"
                    print(f"  * {api_name}: {status}")
        
        print("\n建议:")
        for recommendation in report['recommendations']:
            print(f"- {recommendation}")
            
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")
    finally:
        analyzer.close_browser()

if __name__ == "__main__":
    main()
